package com.geeksec.certificateanalyzer.operator.analysis.scoring;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书风险评分器测试
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
class CertificateRiskScorerTest {

    private CertificateRiskScorer scorer;

    @BeforeEach
    void setUp() {
        scorer = new CertificateRiskScorer();
    }

    @Test
    void testCalculateThreatScore() throws Exception {
        // 创建测试证书
        X509Certificate cert = new X509Certificate();
        
        // 添加威胁标签
        cert.setLabels(Set.of(
            CertificateLabel.MALWARE,      // 威胁评分: 40
            CertificateLabel.PHISHING,     // 威胁评分: 35
            CertificateLabel.FAKE_CERT     // 威胁评分: 30
        ));

        // 执行评分
        X509Certificate result = scorer.map(cert);

        // 验证威胁评分 (40 + 35 + 30 = 105, 但最大值为100)
        assertEquals(100, result.getThreatScore());
        
        // 验证信任评分 (这些标签的信任评分都是0)
        assertEquals(0, result.getTrustScore());
        
        // 验证导入时间已设置
        assertNotNull(result.getImportTime());
    }

    @Test
    void testCalculateTrustScore() throws Exception {
        // 创建测试证书
        X509Certificate cert = new X509Certificate();
        
        // 添加信任标签
        cert.setLabels(Set.of(
            CertificateLabel.TRUSTED_CA_CERT,  // 信任评分: 20
            CertificateLabel.WINDOWS_TRUST,    // 信任评分: 25
            CertificateLabel.EV_CERT          // 信任评分: 25
        ));

        // 执行评分
        X509Certificate result = scorer.map(cert);

        // 验证信任评分 (20 + 25 + 25 = 70)
        assertEquals(70, result.getTrustScore());
        
        // 验证威胁评分 (这些标签的威胁评分都是0)
        assertEquals(0, result.getThreatScore());
    }

    @Test
    void testMixedLabels() throws Exception {
        // 创建测试证书
        X509Certificate cert = new X509Certificate();
        
        // 添加混合标签
        cert.setLabels(Set.of(
            CertificateLabel.SELF_SIGNED_CERT,  // 威胁评分: 10, 信任评分: 0
            CertificateLabel.VALID,             // 威胁评分: 0, 信任评分: 15
            CertificateLabel.STRONG_ALGORITHM   // 威胁评分: 0, 信任评分: 10
        ));

        // 执行评分
        X509Certificate result = scorer.map(cert);

        // 验证威胁评分 (10 + 0 + 0 = 10)
        assertEquals(10, result.getThreatScore());
        
        // 验证信任评分 (0 + 15 + 10 = 25)
        assertEquals(25, result.getTrustScore());
    }

    @Test
    void testEmptyLabels() throws Exception {
        // 创建测试证书
        X509Certificate cert = new X509Certificate();
        cert.setLabels(Set.of());

        // 执行评分
        X509Certificate result = scorer.map(cert);

        // 验证评分都为0
        assertEquals(0, result.getThreatScore());
        assertEquals(0, result.getTrustScore());
    }

    @Test
    void testScoreCapAt100() throws Exception {
        // 创建测试证书
        X509Certificate cert = new X509Certificate();
        
        // 添加高威胁评分标签
        cert.setLabels(Set.of(
            CertificateLabel.APT28_CERT,        // 威胁评分: 50
            CertificateLabel.APT29_CERT,        // 威胁评分: 50
            CertificateLabel.BOTNET_DANABOT,    // 威胁评分: 45
            CertificateLabel.CC_CERT            // 威胁评分: 40
        ));

        // 执行评分
        X509Certificate result = scorer.map(cert);

        // 验证威胁评分被限制在100 (50 + 50 + 45 + 40 = 185, 但最大值为100)
        assertEquals(100, result.getThreatScore());
    }
}
