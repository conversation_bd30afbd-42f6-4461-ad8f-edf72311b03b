package com.geeksec.certificateanalyzer.operator.analysis.compliance;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.flink.api.common.functions.MapFunction;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书合规性验证器
 * 
 * 职责：
 * 1. 验证证书是否符合安全标准和行业规范
 * 2. 检查证书的合规性问题
 * 3. 生成合规性相关的标签
 * 
 * 验证内容包括：
 * - 算法合规性检查
 * - 版本合规性检查
 * - 密钥用途合规性检查
 * - 有效期合规性检查
 * - 证书链合规性检查
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateComplianceValidator implements MapFunction<X509Certificate, X509Certificate> {

    /** 密钥用途数量阈值，超过此值认为是服务器证书用作客户端 */
    private static final int KEY_USAGE_THRESHOLD = 9;
    
    /** 短有效期阈值（天） */
    private static final int SHORT_VALIDITY_THRESHOLD = 30;

    /** 短有效期阈值2（天） */
    private static final long SHORT_VALIDITY_THRESHOLD_90 = 90;

    /** 长有效期阈值（天） */
    private static final int LONG_VALIDITY_THRESHOLD = 1825; // 5年

    /** 两年有效期阈值（天） */
    private static final long TWO_YEARS_THRESHOLD = 366 * 2;

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书合规性验证，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 算法合规性检查
        validateAlgorithmCompliance(certificate, labels);

        // 版本合规性检查
        validateVersionCompliance(certificate, labels);

        // 密钥用途合规性检查
        validateKeyUsageCompliance(certificate, labels);

        // 有效期合规性检查
        validateValidityPeriodCompliance(certificate, labels);

        // 证书类型合规性检查
        validateCertificateTypeCompliance(certificate, labels);

        // 特殊用途合规性检查
        validateSpecialUsageCompliance(certificate, labels);

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 算法合规性检查
     * 检查签名算法是否符合安全标准
     */
    private void validateAlgorithmCompliance(X509Certificate certificate, Set<CertificateLabel> labels) {
        String signatureAlgorithm = certificate.getSignatureAlgName();

        if (signatureAlgorithm == null) {
            return;
        }

        // 检查是否使用了不安全的公钥算法
        if (CertificateConstants.MD5_WITH_RSA.equalsIgnoreCase(signatureAlgorithm)) {
            labels.add(CertificateLabel.INSECURE_PUBKEY);
            log.debug("检测到不安全的公钥算法: {}", signatureAlgorithm);
        }

        // 检查是否使用了弱加密算法
        if (isWeakSignatureAlgorithm(signatureAlgorithm)) {
            labels.add(CertificateLabel.WEAK_ALGORITHM);
            log.debug("检测到弱加密算法: {}", signatureAlgorithm);
        }

        // 检查是否使用了弱签名算法
        String certSignatureAlgorithm = certificate.getSignatureAlgorithm();
        if (certSignatureAlgorithm != null && isWeakSignatureAlgorithm(certSignatureAlgorithm)) {
            labels.add(CertificateLabel.WEAK_ALGORITHM);
            log.debug("检测到弱签名算法: {}", certSignatureAlgorithm);
        }
    }

    /**
     * 版本合规性检查
     * 检查证书版本是否符合标准
     */
    private void validateVersionCompliance(X509Certificate certificate, Set<CertificateLabel> labels) {
        String version = certificate.getVersion();
        if (!CertificateConstants.CERT_VERSION_V3.equals(version)) {
            labels.add(CertificateLabel.INSECURE_VERSION);
            log.debug("检测到不安全的证书版本: {}", version);
        }
    }

    /**
     * 密钥用途合规性检查
     * 检查密钥用途是否合规
     */
    private void validateKeyUsageCompliance(X509Certificate certificate, Set<CertificateLabel> labels) {
        String keyUsage = certificate.getKeyUsage();
        if (keyUsage != null && !keyUsage.isEmpty()) {
            String[] keyUsageItems = keyUsage.split(", ");

            if (keyUsageItems.length >= KEY_USAGE_THRESHOLD) {
                labels.add(CertificateLabel.SERVER_CERT_AS_CLIENT);
                log.debug("检测到服务器证书用作客户端，密钥用途数量: {}", keyUsageItems.length);
            }
        }
    }

    /**
     * 有效期合规性检查
     * 检查证书有效期是否合理
     */
    private void validateValidityPeriodCompliance(X509Certificate certificate, Set<CertificateLabel> labels) {
        LocalDateTime notBefore = certificate.getNotBefore();
        LocalDateTime notAfter = certificate.getNotAfter();

        if (notBefore != null && notAfter != null) {
            Duration validityPeriod = Duration.between(notBefore, notAfter);
            long validityDays = validityPeriod.toDays();

            // 检测短有效期证书（少于30天或90天以下）
            if (validityDays < SHORT_VALIDITY_THRESHOLD || validityDays < SHORT_VALIDITY_THRESHOLD_90) {
                labels.add(CertificateLabel.SHORT_VALIDITY_PERIOD);
                log.debug("检测到短有效期证书: {} 天", validityDays);
            }

            // 检测长有效期证书（超过5年或超过2年）
            if (validityDays > LONG_VALIDITY_THRESHOLD || validityDays > TWO_YEARS_THRESHOLD) {
                labels.add(CertificateLabel.LONG_VALIDITY_PERIOD);
                log.debug("检测到长有效期证书: {} 天", validityDays);
            }

            // 检测证书状态
            LocalDateTime now = LocalDateTime.now();
            if (notAfter.isBefore(now)) {
                labels.add(CertificateLabel.EXPIRED);
                log.debug("检测到过期证书");
            } else {
                labels.add(CertificateLabel.VALID);
            }
        }
    }

    /**
     * 证书类型合规性检查
     * 检查证书类型是否符合标准
     */
    private void validateCertificateTypeCompliance(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 获取基本约束扩展
        String basicConstraints = getExtensionValue(certificate, "basicConstraints");
        String keyUsage = certificate.getKeyUsage();
        List<String> extendedKeyUsage = certificate.getExtendedKeyUsage();

        if (basicConstraints != null && !basicConstraints.contains("CA:TRUE")) {
            // 终端实体证书
            labels.add(CertificateLabel.END_ENTITY);

            if (keyUsage != null && !keyUsage.isEmpty() &&
                    extendedKeyUsage != null && !extendedKeyUsage.isEmpty()) {
                labels.add(CertificateLabel.APP_LEAF_CERT);
            }
        } else {
            // CA证书
            // 检查是否为根证书（自签名）
            if (certificate.getSubjectId() != null &&
                    certificate.getSubjectId().equals(certificate.getIssuerId())) {
                labels.add(CertificateLabel.ROOT_CA);
                labels.add(CertificateLabel.PRIVATE_CA);
            } else {
                labels.add(CertificateLabel.INTERMEDIATE_CA);
                labels.add(CertificateLabel.PRIVATE_CA);
            }
        }
    }

    /**
     * 特殊用途合规性检查
     * 检查证书的特殊用途是否合规
     */
    private void validateSpecialUsageCompliance(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> extendedKeyUsageList = certificate.getExtendedKeyUsage();

        if (extendedKeyUsageList != null && !extendedKeyUsageList.isEmpty()) {
            // 代码签名证书
            if (extendedKeyUsageList.contains("Code Signing")) {
                labels.add(CertificateLabel.CODE_SIGNING_CERT);
            }

            // 邮件保护证书
            if (extendedKeyUsageList.contains("Email Protection")) {
                labels.add(CertificateLabel.EMAIL_CERT);
            }

            // 服务器认证证书
            if (extendedKeyUsageList.contains("Server Authentication")) {
                labels.add(CertificateLabel.SERVER_AUTH_CERT);
            }

            // 客户端认证证书
            if (extendedKeyUsageList.contains("Client Authentication")) {
                labels.add(CertificateLabel.CLIENT_AUTH_CERT);
            }
        }
    }

    /**
     * 获取证书扩展值
     */
    private String getExtensionValue(X509Certificate certificate, String extensionName) {
        if (certificate.getCertificateExtensions() != null &&
                certificate.getCertificateExtensions().getExtensionMap() != null) {
            Object value = certificate.getCertificateExtensions().getExtensionMap().get(extensionName);
            return value != null ? value.toString() : "";
        }
        return "";
    }

    /**
     * 检测弱签名算法
     */
    private boolean isWeakSignatureAlgorithm(String algorithm) {
        if (algorithm == null) {
            return false;
        }

        String lowerAlgorithm = algorithm.toLowerCase();
        return lowerAlgorithm.contains("md5") ||
               lowerAlgorithm.contains("sha1") ||
               lowerAlgorithm.contains("md2") ||
               CertificateConstants.MD5_WITH_RSA.equalsIgnoreCase(algorithm) ||
               CertificateConstants.SHA1_WITH_RSA.equalsIgnoreCase(algorithm);
    }
}
